<route lang="jsonc" type="page">
{
  "layout": "default",
  "style": {
    "navigationStyle": "custom",
    "backgroundColor": "#f8f8f8"
  }
}
</route>

<script lang="ts" setup>
import { useUserStore } from '@/store/user'

defineOptions({
  name: 'Login',
})

// 响应式数据
const loading = ref(false)
const title = ref('证件照制作')

// 微信登录
async function handleWechatLogin() {
  try {
    loading.value = true

    // 使用store中的微信登录方法
    const userStore = useUserStore()
    await userStore.wxLogin()

    uni.showToast({
      title: '登录成功',
      icon: 'success',
    })

    // 登录成功后跳转到首页
    handleGuestMode()
  }
  catch (error) {
    console.error('登录失败:', error)
    uni.showToast({
      title: '登录失败，请重试',
      icon: 'none',
    })
  }
  finally {
    loading.value = false
  }
}

// 暂不登录，直接返回
function handleGuestMode() {
  uni.navigateBack()
}

console.log('login page loaded')
</script>

<template>
  <view class="relative h-screen w-full overflow-hidden from-white to-[#e6e9ff] bg-gradient-to-br">
    <view class="relative z-1 h-[calc(100vh-88rpx)] w-full center flex-col from-transparent to-[rgba(240,242,255,0.5)]">
      <!-- 欢迎文字 -->
      <view class="center flex-col">
        <text class="text-[40rpx] text-[#333] font-bold">
          {{ title }}
        </text>
        <text class="mt-3 text-[28rpx] text-[#666]">
          让证件照制作更简单
        </text>
      </view>

      <!-- 登录按钮 -->
      <wd-button
        type="info"
        block
        custom-class="login-btn"
        :loading="loading"
        @click="handleWechatLogin"
      >
        快捷登录
      </wd-button>

      <!-- 暂不登录 -->
      <view class="mt-5 text-center">
        <text class="text-xs text-[#666] underline" @click="handleGuestMode">
          暂不登录
        </text>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
:deep(.login-btn) {
  width: 600rpx !important;
  height: 88rpx !important;
  border-radius: 44rpx !important;
  margin-top: 80rpx !important;
  background: #8280ff !important;
  border-color: #8280ff !important;
  color: #ffffff !important;
}
</style>
