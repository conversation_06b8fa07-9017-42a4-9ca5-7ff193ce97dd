{"compilerOptions": {"composite": true, "lib": ["esnext", "dom"], "baseUrl": ".", "module": "ESNext", "moduleResolution": "Node", "paths": {"@/*": ["./src/*"], "@img/*": ["./src/static/*"]}, "resolveJsonModule": true, "types": ["@dcloudio/types", "@uni-helper/uni-types", "@types/wechat-miniprogram", "wot-design-uni/global.d.ts", "z-paging/types", "./src/typings.d.ts"], "allowJs": true, "noImplicitThis": true, "outDir": "dist", "sourceMap": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true}, "vueCompilerOptions": {"plugins": ["@uni-helper/uni-types/volar-plugin"]}, "include": ["src/**/*.ts", "src/**/*.js", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.jsx", "src/**/*.vue", "src/**/*.json"], "exclude": ["node_modules"]}